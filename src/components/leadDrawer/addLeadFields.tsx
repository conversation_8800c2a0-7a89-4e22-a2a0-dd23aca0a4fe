import { Input, Select, Textarea } from "@components/common";
import type { FormField } from "./interface";

// biome-ignore lint/suspicious/noExplicitAny: TanStack Form types are too complex for proper typing
export const renderFormField = (field: FormField, form: any) => {
  const BASE_CLASSES = field.colSpan ? `col-span-${field.colSpan}` : "";
  const GRID_CLASSES = field.gridCols
    ? `grid-cols-${field.gridCols}`
    : "grid-cols-2";
  const CONTAINER_CLASSES = `${BASE_CLASSES} grid ${GRID_CLASSES}`;

  const labelContent = (
    <label
      htmlFor={field.id}
      className={`text-h6 ${field.required ? "flex gap-1" : ""}`}
    >
      <span className="text-h6">{field.label}</span>
      {field.required && <span className="text-error text-h6">*</span>}
    </label>
  );

  switch (field.type) {
    case "input": {
      const inputField = field;
      return (
        <form.Field
          name={field.id as keyof typeof form.state.values}
          key={field.id}
          validators={{
            onChange: field.required
              ? ({ value }: { value: string }) =>
                  !value ? `${field.label} is required` : undefined
              : undefined,
          }}
        >
          {/* biome-ignore lint/suspicious/noExplicitAny: TanStack Form FieldApi types are too complex */}
          {(fieldApi: any) => (
            <div className={`group ${CONTAINER_CLASSES}`}>
              {labelContent}
              <Input
                id={fieldApi.name}
                type="text"
                placeholder={inputField.placeholder}
                value={fieldApi.state.value as string}
                onChange={(e) => fieldApi.handleChange(e.target.value)}
                onBlur={fieldApi.handleBlur}
                disabled={inputField.disabled}
                className={`${inputField.gridCols === 3 ? "col-span-2" : ""} flex-1 group-hover:bg-base-200 ${
                  fieldApi.state.meta.errors.length > 0 ? "border-error" : ""
                }`}
                variant={inputField.variant || "default"}
              />
              {fieldApi.state.meta.errors.length > 0 && (
                <div className="col-span-full text-error text-sm">
                  {fieldApi.state.meta.errors[0]}
                </div>
              )}
            </div>
          )}
        </form.Field>
      );
    }

    case "date": {
      const dateField = field;
      return (
        <form.Field
          name={field.id as keyof typeof form.state.values}
          key={field.id}
        >
          {/* biome-ignore lint/suspicious/noExplicitAny: TanStack Form FieldApi types are too complex */}
          {(fieldApi: any) => (
            <div className={CONTAINER_CLASSES}>
              {labelContent}
              <div className="relative">
                <Input
                  id={fieldApi.name}
                  type="date"
                  value={fieldApi.state.value as string}
                  onChange={(e) => fieldApi.handleChange(e.target.value)}
                  onBlur={fieldApi.handleBlur}
                  disabled={dateField.disabled}
                  className="w-full"
                />
              </div>
            </div>
          )}
        </form.Field>
      );
    }

    case "select": {
      const selectField = field;
      return (
        <form.Field
          name={field.id as keyof typeof form.state.values}
          key={field.id}
          validators={{
            onChange: field.required
              ? ({ value }: { value: string }) =>
                  !value ? `${field.label} is required` : undefined
              : undefined,
          }}
        >
          {/* biome-ignore lint/suspicious/noExplicitAny: TanStack Form FieldApi types are too complex */}
          {(fieldApi: any) => (
            <div className={CONTAINER_CLASSES}>
              {labelContent}
              {field.gridCols === 3 ? (
                <div className="col-span-2">
                  <Select
                    id={fieldApi.name}
                    options={selectField.options}
                    size="sm"
                    variant="popup"
                    value={fieldApi.state.value as string}
                    onChange={(value) => fieldApi.handleChange(value)}
                    className={`flex-1 ${
                      fieldApi.state.meta.errors.length > 0
                        ? "border-error"
                        : ""
                    }`}
                    placeholder={selectField.placeholder}
                  />
                  {fieldApi.state.meta.errors.length > 0 && (
                    <div className="col-span-full text-error text-sm">
                      {fieldApi.state.meta.errors[0]}
                    </div>
                  )}
                </div>
              ) : (
                <div>
                  <Select
                    id={fieldApi.name}
                    options={selectField.options}
                    size="sm"
                    variant="popup"
                    value={fieldApi.state.value as string}
                    onChange={(value) => fieldApi.handleChange(value)}
                    className={
                      fieldApi.state.meta.errors.length > 0
                        ? "border-error"
                        : ""
                    }
                    placeholder={selectField.placeholder}
                  />
                  {fieldApi.state.meta.errors.length > 0 && (
                    <div className="text-error text-sm">
                      {fieldApi.state.meta.errors[0]}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </form.Field>
      );
    }

    case "textarea": {
      const textareaFieldTyped = field;
      return (
        <form.Field
          name={field.id as keyof typeof form.state.values}
          key={field.id}
        >
          {/* biome-ignore lint/suspicious/noExplicitAny: TanStack Form FieldApi types are too complex */}
          {(fieldApi: any) => (
            <div className="flex min-h-0 flex-1 flex-col gap-2">
              <label htmlFor={fieldApi.name} className="text-h6">
                {field.label}
              </label>
              <Textarea
                id={fieldApi.name}
                placeholder={textareaFieldTyped.placeholder}
                value={fieldApi.state.value as string}
                onChange={(e) => fieldApi.handleChange(e.target.value)}
                onBlur={fieldApi.handleBlur}
                className={textareaFieldTyped.className}
              />
              {fieldApi.state.meta.errors.length > 0 && (
                <div className="text-error text-sm">
                  {fieldApi.state.meta.errors[0]}
                </div>
              )}
            </div>
          )}
        </form.Field>
      );
    }

    default:
      return null;
  }
};
