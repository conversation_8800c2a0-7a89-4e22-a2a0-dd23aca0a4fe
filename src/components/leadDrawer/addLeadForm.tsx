import {
  _CONTACT_CHANNEL_OPTIONS,
  _FOLLOW_UP_STATUS_OPTIONS,
  _OPPORTUNITY_OPTIONS,
  _SERVICE_OPTIONS,
} from "@components/badge";
import { Button, Input, Select, Textarea } from "@components/common";
import { useForm } from "@tanstack/react-form";
import { useTranslation } from "react-i18next";
import type { FormField, FormValues, TextareaField } from "./interface";

const renderFormField = (field: FormField, form) => {
  const BASE_CLASSES = field.colSpan ? `col-span-${field.colSpan}` : "";
  const GRID_CLASSES = field.gridCols
    ? `grid-cols-${field.gridCols}`
    : "grid-cols-2";
  const CONTAINER_CLASSES = `${BASE_CLASSES} grid ${GRID_CLASSES}`;

  const labelContent = (
    <label
      htmlFor={field.id}
      className={`text-h6 ${field.required ? "flex gap-1" : ""}`}
    >
      <span className="text-h6">{field.label}</span>
      {field.required && <span className="text-error text-h6">*</span>}
    </label>
  );

  switch (field.type) {
    case "input": {
      const inputField = field;
      return (
        <form.Field
          name={field.id as keyof typeof form.state.values}
          key={field.id}
          validators={{
            onChange: field.required
              ? ({ value }: { value: string }) =>
                  !value ? `${field.label} is required` : undefined
              : undefined,
          }}
        >
          {(fieldApi: any) => (
            <div className={`group ${CONTAINER_CLASSES}`}>
              {labelContent}
              <Input
                id={fieldApi.name}
                type="text"
                placeholder={inputField.placeholder}
                value={fieldApi.state.value as string}
                onChange={(e) => fieldApi.handleChange(e.target.value)}
                onBlur={fieldApi.handleBlur}
                disabled={inputField.disabled}
                className={`${inputField.gridCols === 3 ? "col-span-2" : ""} flex-1 group-hover:bg-base-200 ${
                  fieldApi.state.meta.errors.length > 0 ? "border-error" : ""
                }`}
                variant={inputField.variant || "default"}
              />
              {fieldApi.state.meta.errors.length > 0 && (
                <div className="col-span-full text-error text-sm">
                  {fieldApi.state.meta.errors[0]}
                </div>
              )}
            </div>
          )}
        </form.Field>
      );
    }

    case "date": {
      const dateField = field;
      return (
        <form.Field
          name={field.id as keyof typeof form.state.values}
          key={field.id}
        >
          {(fieldApi) => (
            <div className={CONTAINER_CLASSES}>
              {labelContent}
              <div className="relative">
                <Input
                  id={fieldApi.name}
                  type="date"
                  value={fieldApi.state.value as string}
                  onChange={(e) => fieldApi.handleChange(e.target.value)}
                  onBlur={fieldApi.handleBlur}
                  disabled={dateField.disabled}
                  className="w-full"
                />
              </div>
            </div>
          )}
        </form.Field>
      );
    }

    case "select": {
      const selectField = field;
      return (
        <form.Field
          name={field.id as keyof typeof form.state.values}
          key={field.id}
          validators={{
            onChange: field.required
              ? ({ value }: { value: string }) =>
                  !value ? `${field.label} is required` : undefined
              : undefined,
          }}
        >
          {/* biome-ignore lint/suspicious/noExplicitAny: TanStack Form FieldApi types are too complex */}
          {(fieldApi: any) => (
            <div className={CONTAINER_CLASSES}>
              {labelContent}
              {field.gridCols === 3 ? (
                <div className="col-span-2">
                  <Select
                    id={fieldApi.name}
                    options={selectField.options}
                    size="sm"
                    variant="popup"
                    value={fieldApi.state.value as string}
                    onChange={(value) => fieldApi.handleChange(value)}
                    className={`flex-1 ${
                      fieldApi.state.meta.errors.length > 0
                        ? "border-error"
                        : ""
                    }`}
                    placeholder={selectField.placeholder}
                  />
                  {fieldApi.state.meta.errors.length > 0 && (
                    <div className="col-span-full text-error text-sm">
                      {fieldApi.state.meta.errors[0]}
                    </div>
                  )}
                </div>
              ) : (
                <div>
                  <Select
                    id={fieldApi.name}
                    options={selectField.options}
                    size="sm"
                    variant="popup"
                    value={fieldApi.state.value as string}
                    onChange={(value) => fieldApi.handleChange(value)}
                    className={
                      fieldApi.state.meta.errors.length > 0
                        ? "border-error"
                        : ""
                    }
                    placeholder={selectField.placeholder}
                  />
                  {fieldApi.state.meta.errors.length > 0 && (
                    <div className="text-error text-sm">
                      {fieldApi.state.meta.errors[0]}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </form.Field>
      );
    }

    case "textarea": {
      const textareaFieldTyped = field;
      return (
        <form.Field
          name={field.id as keyof typeof form.state.values}
          key={field.id}
        >
          {(fieldApi: any) => (
            <div className="flex min-h-0 flex-1 flex-col gap-2">
              <label htmlFor={fieldApi.name} className="text-h6">
                {field.label}
              </label>
              <Textarea
                id={fieldApi.name}
                placeholder={textareaFieldTyped.placeholder}
                value={fieldApi.state.value as string}
                onChange={(e) => fieldApi.handleChange(e.target.value)}
                onBlur={fieldApi.handleBlur}
                className={textareaFieldTyped.className}
              />
              {fieldApi.state.meta.errors.length > 0 && (
                <div className="text-error text-sm">
                  {fieldApi.state.meta.errors[0]}
                </div>
              )}
            </div>
          )}
        </form.Field>
      );
    }

    default:
      return null;
  }
};

export const AddLeadForm = () => {
  const { t } = useTranslation();

  const today = new Date().toISOString().split("T")[0];

  const form = useForm({
    defaultValues: {
      contactChannel: "",
      contactInfo: "",
      followUpDate: "",
      followUpStatus: "",
      name: "",
      note: "",
      opportunity: "",
      servicesOfInterest: "",
      startDate: today,
    },
    onSubmit: async ({ value }: { value: FormValues }) => {
      alert(JSON.stringify(value, null, 2));
    },
  });

  const FormField: FormField[] = [
    {
      colSpan: 2,
      gridCols: 3,
      id: "name",
      label: t("addLead.name"),
      placeholder: t("addLead.name"),
      required: true,
      type: "input",
      variant: "transparent",
    },
    {
      colSpan: 1,
      gridCols: 2,
      id: "opportunity",
      label: t("addLead.opportunity"),
      options: _OPPORTUNITY_OPTIONS,
      placeholder: t("addLead.opportunity"),
      type: "select",
    },
    {
      colSpan: 2,
      gridCols: 3,
      id: "followUpStatus",
      label: t("addLead.followUpStatus"),
      options: _FOLLOW_UP_STATUS_OPTIONS,
      placeholder: t("addLead.followUpStatus"),
      type: "select",
    },
    {
      colSpan: 1,
      gridCols: 2,
      id: "contactChannel",
      label: t("addLead.contactChannel"),
      options: _CONTACT_CHANNEL_OPTIONS,
      placeholder: t("addLead.contactChannel"),
      required: true,
      type: "select",
    },
    {
      colSpan: 2,
      gridCols: 3,
      id: "servicesOfInterest",
      label: t("addLead.servicesOfInterest"),
      options: _SERVICE_OPTIONS,
      placeholder: t("addLead.servicesOfInterest"),
      type: "select",
    },
    {
      colSpan: 1,
      defaultValue: today,
      gridCols: 2,
      id: "startDate",
      label: t("addLead.startDate"),
      type: "date",
    },
    {
      colSpan: 2,
      gridCols: 3,
      id: "contactInfo",
      label: t("addLead.contactInfo"),
      placeholder: t("addLead.contactInfo"),
      type: "input",
      variant: "transparent",
    },
    {
      colSpan: 1,
      defaultValue: "-",
      disabled: true,
      gridCols: 2,
      id: "followUpDate",
      label: t("addLead.followUpDate"),
      type: "input",
    },
  ];

  const textareaField: TextareaField = {
    className: "w-full min-h-[120px] resize-none",
    id: "note",
    label: t("addLead.note"),
    placeholder: t("addLead.note"),
    type: "textarea",
  };

  return (
    <form
      className="flex h-full flex-col gap-6 overflow-hidden p-1"
      onSubmit={(e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          {FormField.map((field) => renderFormField(field, form))}
        </div>
      </div>

      {/* บันทึก */}
      {renderFormField(textareaField, form)}

      {/* Debug Info */}
      <form.Subscribe
        selector={(state) => [state.values, state.errors, state.canSubmit]}
      >
        {([values, errors, canSubmit]) => (
          <div className="rounded bg-gray-100 p-2 text-gray-500 text-xs">
            <div>Can Submit: {canSubmit ? "Yes" : "No"}</div>
            <div>Errors: {JSON.stringify(errors)}</div>
            <div>Values: {JSON.stringify(values)}</div>
          </div>
        )}
      </form.Subscribe>

      {/* Buttons */}
      <div className="flex w-full justify-end gap-3 pt-4">
        <Button variant="outline" className="w-28" type="button">
          {t("common.draft")}
        </Button>
        <form.Subscribe
          selector={(state) => [state.canSubmit, state.isSubmitting]}
        >
          {([canSubmit, isSubmitting]) => (
            <Button
              className="w-28"
              type="submit"
              disabled={!canSubmit || isSubmitting}
            >
              {isSubmitting ? t("common.loading") : t("common.save")}
            </Button>
          )}
        </form.Subscribe>
      </div>
    </form>
  );
};
